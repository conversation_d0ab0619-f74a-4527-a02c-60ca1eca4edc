<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Modules\Author\app\Repositories\AuthorRepository;
use Modules\Book\app\Repositories\BookRepository;
use Modules\Award\app\Repositories\AwardRepository;
use Modules\CriticalAcclaim\app\Repositories\CriticalAcclaimRepository;

class AuthorController extends Controller
{
    protected $authorRepository;
    protected $bookRepository;
    protected $awardRepository;
    protected $criticalAcclaimRepository;

    public function __construct(
        AuthorRepository $authorRepository,
        BookRepository $bookRepository,
        AwardRepository $awardRepository,
        CriticalAcclaimRepository $criticalAcclaimRepository
    ) {
        $this->authorRepository = $authorRepository;
        $this->bookRepository = $bookRepository;
        $this->awardRepository = $awardRepository;
        $this->criticalAcclaimRepository = $criticalAcclaimRepository;
    }
    /**
     * Display all authors with pagination and filtering.
     */
    public function index(): View
    {
        $authors = $this->authorRepository->getPaginatedActiveAuthors(15);
        return view('frontend.authors.index', compact('authors'));
    }

    /**
     * Display a specific author profile.
     */
    public function show(string $authorSlug): View
    {
        // Get author with their books
        // $author = Author::with(['books' => function ($query) {
        //     $query->where('status', 'published')->latest();
        // }])->where('slug', $authorSlug)->where('status', 'active')->firstOrFail();

        return view('frontend.authors.show', [
            // 'author' => $author,
        ]);
    }
}
